"""
FastAPI Application Factory

Creates and configures the FastAPI application with all necessary middleware,
routers, and dependencies.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

from ..core.config import get_config
from ..utils.logging import RichLogger
from .database import init_db, close_db
from .routers import status, conversations, pool
from .middleware import AuthenticationMiddleware
from .exceptions import APIException, ValidationException, NotFoundError, ConflictError

# Create logger
logger = RichLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("🚀 Starting ChatGPT Controller API...")

    # Initialize database
    await init_db()
    logger.info("📊 Database initialized")

    # Setup event handlers for conversation monitoring
    from .utils.event_handlers import setup_conversation_event_listeners
    setup_conversation_event_listeners()
    logger.info("📡 Event handlers registered")

    # Start pending conversation manager
    from .utils.pending_conversation_manager import pending_manager
    try:
        # Set WebSocket server if available
        if hasattr(app.state, 'websocket_server') and app.state.websocket_server:
            pending_manager.set_websocket_server(app.state.websocket_server)

        await pending_manager.start()
        logger.info("🔄 Pending conversation manager started")
    except Exception as e:
        logger.warning(f"⚠️ Failed to start pending conversation manager: {e}")

    # Store app state (only if not already set)
    if not hasattr(app.state, 'websocket_server'):
        app.state.websocket_server = None
    if not hasattr(app.state, 'sdk_client'):
        app.state.sdk_client = None
    if not hasattr(app.state, 'monitoring_service'):
        app.state.monitoring_service = None

    yield

    # Cleanup
    logger.info("🛑 Shutting down ChatGPT Controller API...")

    # Stop pending conversation manager
    try:
        await pending_manager.stop()
        logger.info("🔄 Pending conversation manager stopped")
    except Exception as e:
        logger.warning(f"⚠️ Error stopping pending conversation manager: {e}")

    await close_db()
    logger.info("📊 Database connections closed")


def create_app(config=None) -> FastAPI:
    """
    Create and configure FastAPI application
    
    Args:
        config: Optional configuration object
        
    Returns:
        Configured FastAPI application
    """
    if config is None:
        config = get_config()
    
    # Create FastAPI app
    app = FastAPI(
        title="ChatGPT Controller API",
        description="REST API for ChatGPT Forward Plugin Controller",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )
    
    # Add middleware
    setup_middleware(app, config)
    
    # Add routers
    setup_routers(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Store config in app state
    app.state.config = config
    
    logger.info("✅ FastAPI application created successfully")
    return app


def setup_middleware(app: FastAPI, config):
    """Setup application middleware"""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure based on your needs
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure based on your needs
    )
    
    # Custom middleware
    app.add_middleware(AuthenticationMiddleware)


def setup_routers(app: FastAPI):
    """Setup API routers"""
    
    # Include routers
    app.include_router(
        status.router,
        prefix="/api/v1",
    )
    
    app.include_router(
        conversations.router,
        prefix="/api/v1",
    )
    
    app.include_router(
        pool.router,
        prefix="/api/v1",
    )


def setup_exception_handlers(app: FastAPI):
    """Setup custom exception handlers"""
    
    @app.exception_handler(APIException)
    async def api_exception_handler(request: Request, exc: APIException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "type": exc.__class__.__name__,
                    "message": exc.message,
                    "details": exc.details,
                    "timestamp": exc.timestamp.isoformat(),
                }
            }
        )
    
    @app.exception_handler(ValidationException)
    async def validation_exception_handler(request: Request, exc: ValidationException):
        return JSONResponse(
            status_code=422,
            content={
                "error": {
                    "type": "ValidationError",
                    "message": "Request validation failed",
                    "details": exc.details,
                    "timestamp": exc.timestamp.isoformat(),
                }
            }
        )
    
    @app.exception_handler(NotFoundError)
    async def not_found_handler(request: Request, exc: NotFoundError):
        return JSONResponse(
            status_code=404,
            content={
                "error": {
                    "type": "NotFoundError",
                    "message": exc.message,
                    "details": exc.details,
                    "timestamp": exc.timestamp.isoformat(),
                }
            }
        )
    
    @app.exception_handler(ConflictError)
    async def conflict_handler(request: Request, exc: ConflictError):
        return JSONResponse(
            status_code=409,
            content={
                "error": {
                    "type": "ConflictError",
                    "message": exc.message,
                    "details": exc.details,
                    "timestamp": exc.timestamp.isoformat(),
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger.error(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An internal server error occurred",
                    "details": None,
                }
            }
        )


# Custom OpenAPI schema
def custom_openapi(app: FastAPI):
    """Generate custom OpenAPI schema"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="ChatGPT Controller API",
        version="1.0.0",
        description="REST API for ChatGPT Forward Plugin Controller with WebSocket integration",
        routes=app.routes,
    )
    
    # Add custom info
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema
