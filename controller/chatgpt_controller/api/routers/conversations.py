"""
Conversations API Router

Provides endpoints for managing ChatGPT conversations including starting,
querying, and continuing conversations.
"""

import asyncio
import traceback
from datetime import datetime
from urllib.parse import quote

from fastapi import APIRouter, Depends, Request, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from ..database import get_db
from ..models import Conversation, Message
from ..schemas import (
    ConversationStartRequest,
    ConversationStartResponse,
    MessageRequest,
    MessageResponse,
    ConversationResponse,
    ConversationListResponse,
    ConversationData,
    MessageData,
    ConversationQueryParams,
)
from ..exceptions import (
    NotFoundError,
    BrowserControlError,
)
from ..utils.browser_utils import (
    extract_conversation_id_from_url,
    simulate_keyboard_by_xpath,
    wait_url_match_regex,
    wait_for_page_load,
    get_current_url,
    click_element_by_xpath,
    input_text_by_xpath,
)
from ...utils.logging import RichLogger
from ...server.websocket_server import WebSocketServer
from ...core.message_types import MessageType
from ..utils.timestamp_resolver import timestamp_resolver
from ..utils.pending_conversation_manager import pending_manager
from ..models import ConversationStatus

# Event handlers are initialized in the FastAPI app startup

# Create router
router = APIRouter()
logger = RichLogger(__name__)

SUBMIT_BUTTON_XPATH = '//*[@id="composer-submit-button"]'
TEXTAREA_XPATH = "//div[@id='prompt-textarea']"


def get_websocket_server(request: Request) -> WebSocketServer:
    """Get WebSocket server from app state"""
    websocket_server = getattr(request.app.state, "websocket_server", None)
    if not websocket_server:
        raise ConnectionError("WebSocket server not available")

    if websocket_server is None:
        raise ConnectionError("WebSocket server is None, not available")
    return websocket_server


@router.post(
    "/conversations/start",
    response_model=ConversationStartResponse,
    tags=["对话管理"],
    summary="启动新的ChatGPT对话",
    description="""
    启动一个新的ChatGPT对话会话。

    此端点将：
    1. 构建带有指定参数的ChatGPT URL
    2. 通过WebSocket向浏览器扩展发送打开命令
    3. 等待页面加载并尝试提交初始提示
    4. 提取对话ID并在数据库中创建记录
    5. 返回对话详情和访问URL

    **注意事项：**
    - 需要浏览器扩展连接到WebSocket服务器
    - 初始提示会被URL编码并作为查询参数传递
    - 如果页面加载超时，系统会继续执行但可能影响对话创建
    """,
    responses={
        200: {
            "description": "对话启动成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Conversation started successfully",
                        "conversation_id": "conv-1234567890",
                        "url": "https://chatgpt.com/c/conv-1234567890",
                        "redirect_url": "https://chatgpt.com/c/conv-1234567890",
                        "timestamp": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Browser control operation 'open_chatgpt' failed: No browser extension connected"
                    }
                }
            },
        },
    },
)
async def start_conversation(
    request_data: ConversationStartRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """
    启动新的ChatGPT对话

    创建一个新的ChatGPT对话会话，支持指定对话模式和初始提示。
    系统会自动打开ChatGPT页面，提交初始提示，并跟踪对话状态。

    Args:
        request_data: 对话启动参数，包含模式和初始提示
        request: HTTP请求对象，用于获取WebSocket服务器
        db: 数据库会话

    Returns:
        ConversationStartResponse: 新对话的详细信息，包含对话ID和访问URL

    Raises:
        HTTPException: 当浏览器控制失败、连接错误或其他内部错误时抛出
    """
    try:
        websocket_server = get_websocket_server(request)

        # Build ChatGPT URL with parameters
        base_url = "https://chatgpt.com/"
        params = []

        if request_data.mode:
            params.append(f"hints={request_data.mode.value}")

        # URL encode the prompt
        encoded_prompt = quote(request_data.init_prompt)
        params.append(f"q={encoded_prompt}")

        url = base_url + "?" + "&".join(params)

        logger.info(f"🚀 Starting conversation with URL: {url}")

        # Send command to browser extension via WebSocket server
        try:
            from ...core.message_types import MessageType

            logger.debug("Make sure fresh start from nil. Reset to google.com")
            command_data = {
                "url": "https://www.google.com",
                "newTab": False,
                "focus": True,
            }
            success = await websocket_server.send_command(
                MessageType.OPEN_CHATGPT, command_data
            )
            if not success:
                raise BrowserControlError(
                    "open_chatgpt", "No browser extension connected"
                )
            logger.debug("✅ Successfully sent open google command")

        except Exception as e:
            logger.error(f"❌ Exception during open google: {e}")
            logger.error(f"🔍 Traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=500, detail=f"Internal server error: {str(e)}"
            )

        await asyncio.sleep(2.0)

        try:
            logger.debug(f"🌐 Attempting to open ChatGPT with URL: {url}")
            logger.debug(f"🌐 URL length: {len(url)} characters")

            # Send command to browser extension via WebSocket server
            from ...core.message_types import MessageType

            command_data = {
                "url": url,
                "newTab": False,
                "focus": True,
            }

            success = await websocket_server.send_command(
                MessageType.OPEN_CHATGPT, command_data
            )

            if not success:
                raise BrowserControlError(
                    "open_chatgpt", "No browser extension connected"
                )

            logger.info("✅ Successfully sent open ChatGPT command")

        except Exception as e:
            logger.error(f"❌ Exception during open_chatgpt: {e}")
            logger.error(f"🔍 Traceback: {traceback.format_exc()}")
            if isinstance(e, BrowserControlError):
                raise
            else:
                raise BrowserControlError("open_chatgpt", f"Unexpected error: {str(e)}")

        # Wait for page to load completely
        logger.info("⏳ Waiting for page to load...")
        page_loaded = await wait_for_page_load(websocket_server, timeout=10.0)
        if not page_loaded:
            logger.warning("⚠️ Page load timeout, continuing anyway...")
        else:
            logger.info("✅ Page loaded successfully")

        # await asyncio.sleep(8.0)
        await wait_url_match_regex(
            websocket_server,
            r"https://chatgpt\.com/c/[a-f0-9-]+",
            timeout=30.0,
            interval=1.0,
            # once_fail_fallback=lambda: click_element_by_xpath(
            #     websocket_server, SUBMIT_BUTTON_XPATH, timeout=10.0
            # ),
            once_fail_fallback=lambda: simulate_keyboard_by_xpath(
                websocket_server, TEXTAREA_XPATH, "Enter", timeout=10.0
            )
        )

        # Get current URL to check if we need to submit
        current_url = await get_current_url(websocket_server, timeout=10.0)
        logger.info(f"🌐 Current URL: {current_url}")

        conversation_id = None
        final_url = current_url or url

        # Check if conversation is already started by checking for conversation ID in URL
        if current_url:
            conversation_id = extract_conversation_id_from_url(current_url)

        page_loaded = await wait_for_page_load(websocket_server, timeout=10.0)
        if not page_loaded:
            logger.warning("⚠️ Page load timeout, continuing anyway...")
        else:
            logger.info("✅ Page loaded successfully")

        # If URL does not contain "c/" (no conversation ID), try to click submit button
        if not conversation_id and current_url and "/c/" not in current_url:
            logger.info(
                "🔘 No conversation ID found, attempting to click submit button..."
            )

            # Try to click the submit button
            # success = await click_element_by_xpath(
            #     websocket_server, SUBMIT_BUTTON_XPATH, timeout=10.0
            # )
            success = await simulate_keyboard_by_xpath(
                websocket_server, TEXTAREA_XPATH, "Enter", timeout=10.0
            )

            if success:
                logger.info("✅ Submit button clicked successfully")

                # Wait for page to redirect to conversation URL
                logger.info("⏳ Waiting for conversation to start...")
                for _ in range(30):  # Wait up to 30 seconds
                    await asyncio.sleep(1.0)
                    current_url = await get_current_url(websocket_server, timeout=5.0)
                    if current_url:
                        conversation_id = extract_conversation_id_from_url(current_url)
                        if conversation_id:
                            final_url = current_url
                            logger.info(
                                f"🎯 Conversation started with ID: {conversation_id}"
                            )
                            break

                if not conversation_id:
                    logger.warning(
                        "⚠️ Conversation ID not found after submit, generating fallback ID"
                    )
            else:
                logger.warning("⚠️ Failed to click submit button")

        # Generate fallback conversation ID if not extracted from URL
        if not conversation_id:
            conversation_id = f"conv-{int(datetime.now().timestamp())}"
            logger.info(f"🔄 Generated fallback conversation ID: {conversation_id}")

        logger.info("📄 Command sent to browser extension")
        logger.info(f"🎯 Final conversation ID: {conversation_id}")
        logger.info(f"🌐 Final URL: {final_url}")

        # Brief wait for any final page updates
        await asyncio.sleep(1.0)

        # Create conversation record in database
        try:
            logger.debug("💾 Creating conversation record in database...")
            conversation = Conversation(
                id=conversation_id,
                mode=request_data.mode.value if request_data.mode else None,
                init_prompt=request_data.init_prompt,
                status="active",
                url=url,
                redirect_url=final_url,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

            db.add(conversation)
            await db.commit()

            logger.debug("💾 Database record created successfully")
            logger.success(f"✅ Conversation {conversation_id} started successfully")
        except Exception as e:
            logger.error(f"❌ Database error: {e}")
            logger.error(f"🔍 Database traceback: {traceback.format_exc()}")
            await db.rollback()
            raise BrowserControlError("database_operation", f"Database error: {str(e)}")

        # 添加到后台更新池
        try:
            await pending_manager.add_pending_conversation(conversation_id, auto_config=True)
            logger.info(f"➕ Added conversation {conversation_id} to pending update pool")
        except Exception as e:
            logger.warning(f"⚠️ Failed to add conversation to pending pool: {e}")

        return ConversationStartResponse(
            success=True,
            message="Conversation started successfully",
            conversation_id=conversation_id,
            url=final_url,
            redirect_url=final_url if final_url != url else None,
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except BrowserControlError as e:
        logger.error(f"❌ Browser control error in start_conversation: {e}")
        logger.error(f"🔍 Browser control traceback: {traceback.format_exc()}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Browser control operation '{e.operation}' failed: {e.error_message}",
        )
    except ConnectionError as e:
        logger.error(f"❌ Connection error in start_conversation: {e}")
        logger.error(f"🔍 Connection traceback: {traceback.format_exc()}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Connection error: {str(e)}")
    except Exception as e:
        logger.error(f"❌ Unexpected error in start_conversation: {e}")
        logger.error(f"🔍 Unexpected traceback: {traceback.format_exc()}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get(
    "/conversations",
    response_model=ConversationListResponse,
    tags=["对话管理"],
    summary="获取对话列表",
    description="""
    获取对话列表，支持多种过滤条件和分页功能。

    **支持的过滤条件：**
    - `status`: 按对话状态过滤（active, archived, deleted）
    - `mode`: 按对话模式过滤（research, reason, search, canvas, picture_v2）
    - `search`: 在对话标题和初始提示中搜索关键词
    - `created_after`: 获取指定时间之后创建的对话
    - `created_before`: 获取指定时间之前创建的对话

    **分页参数：**
    - `page`: 页码（从1开始）
    - `page_size`: 每页数量（1-100，默认50）

    **排序：**
    - 按创建时间倒序排列（最新的在前）
    """,
    responses={
        200: {
            "description": "成功获取对话列表",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "conversations": [
                            {
                                "id": "conv-1234567890",
                                "title": "量子计算讨论",
                                "mode": "research",
                                "init_prompt": "帮我了解量子计算的基本原理",
                                "status": "active",
                                "is_cached": True,
                                "created_at": "2024-01-01T12:00:00Z",
                                "updated_at": "2024-01-01T12:30:00Z",
                                "last_accessed": "2024-01-01T12:30:00Z",
                                "url": "https://chatgpt.com/?q=...",
                                "redirect_url": "https://chatgpt.com/c/conv-1234567890",
                                "message_count": 5,
                            }
                        ],
                        "total": 1,
                        "page": 1,
                        "page_size": 50,
                        "timestamp": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Failed to list conversations: Database connection error"
                    }
                }
            },
        },
    },
)
async def list_conversations(
    params: ConversationQueryParams = Depends(),
    db: AsyncSession = Depends(get_db),
):
    """
    获取对话列表

    支持多种过滤条件和分页的对话列表查询。可以按状态、模式、关键词、
    创建时间等条件过滤对话，并支持分页浏览。

    Args:
        params: 查询参数，包含过滤条件和分页设置
        db: 数据库会话

    Returns:
        ConversationListResponse: 包含对话列表、总数和分页信息的响应

    Raises:
        HTTPException: 当数据库查询失败时抛出500错误
    """
    try:
        # Build query conditions
        conditions = []

        if params.status:
            conditions.append(Conversation.status == params.status.value)

        if params.mode:
            conditions.append(Conversation.mode == params.mode.value)

        if params.search:
            search_term = f"%{params.search}%"
            conditions.append(
                or_(
                    Conversation.title.ilike(search_term),
                    Conversation.init_prompt.ilike(search_term),
                )
            )

        if params.created_after:
            conditions.append(Conversation.created_at >= params.created_after)

        if params.created_before:
            conditions.append(Conversation.created_at <= params.created_before)

        # Build base query
        query = select(Conversation)
        if conditions:
            query = query.where(and_(*conditions))

        # Add ordering
        query = query.order_by(Conversation.created_at.desc())

        # Get total count
        count_query = select(Conversation.id)
        if conditions:
            count_query = count_query.where(and_(*conditions))

        total_result = await db.execute(count_query)
        total = len(total_result.fetchall())

        # Add pagination
        offset = (params.page - 1) * params.page_size
        query = query.offset(offset).limit(params.page_size)

        # Execute query
        result = await db.execute(query)
        conversations = result.scalars().all()

        # Convert to response format
        conversation_data = [
            ConversationData(
                id=conv.id,
                title=conv.title,
                mode=conv.mode,
                init_prompt=conv.init_prompt,
                status=conv.status,
                is_cached=conv.is_cached,
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                last_accessed=conv.last_accessed,
                url=conv.url,
                redirect_url=conv.redirect_url,
                message_count=0,  # TODO: Add message count query
            )
            for conv in conversations
        ]

        return ConversationListResponse(
            success=True,
            conversations=conversation_data,
            total=total,
            page=params.page,
            page_size=params.page_size,
        )

    except Exception as e:
        logger.error(f"❌ Error listing conversations: {e}")
        logger.error(f"🔍 Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list conversations: {str(e)}"
        )


@router.get(
    "/conversations/{conversation_id}",
    response_model=ConversationResponse,
    tags=["对话管理"],
    summary="获取对话详情",
    description="""
    获取指定对话的详细内容，包括对话信息和所有消息。

    **智能缓存机制：**
    - 当 `use_cache=true` 且存在缓存数据时，直接返回缓存内容
    - 当 `use_cache=false` 或无缓存时，从浏览器实时获取最新内容
    - 实时获取会打开对话页面并等待API监控捕获数据

    **获取流程：**
    1. 检查数据库中的对话记录
    2. 根据缓存策略决定数据源
    3. 如需实时获取，通过WebSocket打开对话页面
    4. 等待页面加载并捕获API响应
    5. 返回对话数据和消息列表

    **注意事项：**
    - 可能需要等待几秒钟以获取完整数据
    """,
    responses={
        200: {
            "description": "成功获取对话详情",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "conversation": {
                            "id": "conv-1234567890",
                            "title": "量子计算讨论",
                            "mode": "research",
                            "init_prompt": "帮我了解量子计算的基本原理",
                            "status": "active",
                            "is_cached": True,
                            "created_at": "2024-01-01T12:00:00Z",
                            "updated_at": "2024-01-01T12:30:00Z",
                            "last_accessed": "2024-01-01T12:30:00Z",
                            "url": "https://chatgpt.com/?q=...",
                            "redirect_url": "https://chatgpt.com/c/conv-1234567890",
                            "message_count": 2,
                        },
                        "messages": [
                            {
                                "id": 1,
                                "conversation_id": "conv-1234567890",
                                "role": "user",
                                "content": "帮我了解量子计算的基本原理",
                                "content_type": "text",
                                "created_at": "2024-01-01T12:00:00Z",
                                "sent_at": "2024-01-01T12:00:00Z",
                                "metadata": {},
                            },
                            {
                                "id": 2,
                                "conversation_id": "conv-1234567890",
                                "role": "assistant",
                                "content": "量子计算是一种基于量子力学原理的计算方式...",
                                "content_type": "text",
                                "created_at": "2024-01-01T12:01:00Z",
                                "metadata": {},
                            },
                        ],
                        "from_cache": True,
                        "timestamp": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        404: {
            "description": "对话不存在",
            "content": {
                "application/json": {
                    "example": {"detail": "Conversation conv-1234567890 not found"}
                }
            },
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Browser control error: No browser extension connected"
                    }
                }
            },
        },
    },
)
async def get_conversation(
    conversation_id: str,
    request: Request,
    db: AsyncSession = Depends(get_db),
    use_cache: bool = Query(
        True,
        description="是否使用缓存数据（如果可用）。设为false将强制从浏览器获取最新内容",
    ),
):
    """
    获取对话详情

    获取指定对话的完整信息，包括对话元数据和所有消息内容。
    支持智能缓存机制，可选择使用缓存数据或实时获取最新内容。

    Args:
        conversation_id: 对话ID
        request: HTTP请求对象，用于获取WebSocket服务器
        db: 数据库会话
        use_cache: 是否使用缓存数据，默认为True

    Returns:
        ConversationResponse: 包含对话信息和消息列表的响应

    Raises:
        HTTPException:
            - 404: 对话不存在
            - 500: 浏览器控制错误或其他内部错误
    """
    try:
        websocket_server = get_websocket_server(request)

        # First, try to get conversation from database
        query = select(Conversation).where(Conversation.id == conversation_id)
        result = await db.execute(query)
        conversation = result.scalar_one_or_none()

        if not conversation:
            raise NotFoundError(f"Conversation {conversation_id} not found")

        # If use_cache is True and we have cached data, return it
        if use_cache and conversation.is_cached:
            logger.info(f"📋 Using cached data for conversation {conversation_id}")

            # Get messages from database
            messages_query = select(Message).where(
                Message.conversation_id == conversation_id
            )
            messages_result = await db.execute(messages_query)
            messages = messages_result.scalars().all()

            message_data = [
                MessageData(
                    id=msg.id,
                    conversation_id=msg.conversation_id,
                    role=msg.role,
                    content=msg.content,
                    content_type=msg.content_type or "text",
                    message_id=msg.message_id,
                    parent_id=msg.parent_id,
                    model=msg.model,
                    source=msg.source or "user",
                    created_at=msg.created_at,
                    sent_at=msg.sent_at,
                    metadata=msg.message_metadata or {},
                )
                for msg in messages
            ]

            conversation_data = ConversationData(
                id=conversation.id,
                title=conversation.title,
                mode=conversation.mode,
                init_prompt=conversation.init_prompt,
                status=conversation.status,
                is_cached=conversation.is_cached,
                created_at=conversation.created_at,
                updated_at=conversation.updated_at,
                last_accessed=conversation.last_accessed,
                url=conversation.url,
                redirect_url=conversation.redirect_url,
                message_count=len(message_data),
            )

            return ConversationResponse(
                success=True,
                conversation=conversation_data,
                messages=message_data,
                from_cache=True,
            )

        # Otherwise, get fresh content from browser extension
        logger.info(f"🌐 Getting fresh content for conversation {conversation_id}")

        # Open the conversation URL
        conversation_url = f"https://chatgpt.com/c/{conversation_id}"
        logger.info(f"🚀 Opening conversation URL: {conversation_url}")

        # Send command to open the conversation
        command_data = {"url": conversation_url, "newTab": False, "focus": True}

        success = await websocket_server.send_command(
            MessageType.OPEN_CHATGPT, command_data
        )

        if not success:
            raise BrowserControlError("open_chatgpt", "No browser extension connected")

        # Wait for page to load
        logger.info("⏳ Waiting for conversation page to load...")
        page_loaded = await wait_for_page_load(websocket_server, timeout=10.0)
        if not page_loaded:
            logger.warning("⚠️ Page load timeout")

        # Wait a moment for the page to fully load
        await asyncio.sleep(1.0)

        # Verify we're on the correct conversation page
        current_url = await get_current_url(websocket_server, timeout=10.0)
        if current_url and conversation_id not in current_url:
            logger.warning(
                f"⚠️ URL mismatch: expected {conversation_id}, got {current_url}"
            )

        # Extract conversation content from the page
        logger.info("🔍 Extracting conversation content from page...")

        # Try to trigger a conversation API request by checking if we're monitoring this conversation
        # The browser extension should automatically capture API responses
        logger.info("⏳ Waiting for conversation data to be captured...")

        # Wait for up to 10 seconds for the conversation to be updated via API monitoring
        for _ in range(10):
            await asyncio.sleep(1.0)

            # Refresh conversation data from database
            fresh_query = select(Conversation).where(Conversation.id == conversation_id)
            fresh_result = await db.execute(fresh_query)
            fresh_conversation = fresh_result.scalar_one_or_none()

            if fresh_conversation and fresh_conversation.is_cached:
                logger.info("✅ Conversation data captured via API monitoring")
                conversation = fresh_conversation
                break

        # Get messages from database
        messages_query = select(Message).where(
            Message.conversation_id == conversation_id
        )
        messages_result = await db.execute(messages_query)
        messages = messages_result.scalars().all()

        message_data = [
            MessageData(
                id=msg.id,
                conversation_id=msg.conversation_id,
                role=msg.role,
                content=msg.content,
                content_type=msg.content_type,
                message_id=msg.message_id,
                parent_id=msg.parent_id,
                model=msg.model,
                source=msg.source or "user",
                created_at=msg.created_at,
                sent_at=msg.sent_at,
                metadata=msg.message_metadata or {},
            )
            for msg in messages
        ]

        conversation_data = ConversationData(
            id=conversation.id,
            title=conversation.title,
            mode=conversation.mode,
            init_prompt=conversation.init_prompt,
            status=conversation.status,
            is_cached=conversation.is_cached,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at,
            last_accessed=datetime.now(),
            url=conversation.url,
            redirect_url=current_url or conversation.redirect_url,
            message_count=len(message_data),
        )

        # Update last accessed time
        conversation.last_accessed = datetime.now()
        await db.commit()

        return ConversationResponse(
            success=True,
            message="success",
            conversation=conversation_data,
            messages=message_data,
            from_cache=conversation.is_cached,
        )

    except HTTPException:
        raise
    except NotFoundError as e:
        logger.error(f"❌ Conversation not found: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except BrowserControlError as e:
        logger.error(f"❌ Browser control error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Browser control error: {e.error_message}"
        )
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_conversation: {e}")
        logger.error(f"🔍 Traceback: {traceback.format_exc()}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post(
    "/conversations/{conversation_id}/messages",
    response_model=MessageResponse,
    tags=["消息管理"],
    summary="发送消息到对话",
    description="""
    向指定的对话发送新消息。

    **发送流程：**
    1. 验证对话是否存在
    2. 通过WebSocket打开对话页面
    3. 等待页面加载完成
    4. 在输入框中输入消息内容
    5. 点击发送按钮提交消息
    6. 在数据库中记录用户消息
    7. 更新对话的最后访问时间

    **注意事项：**
    - 需要浏览器扩展连接到WebSocket服务器
    - 消息长度限制为1-10000字符
    - 发送后ChatGPT的回复会通过API监控自动捕获
    """,
    responses={
        200: {
            "description": "消息发送成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Message sent successfully",
                        "message_id": "123",
                        "sent_at": "2024-01-01T12:00:00Z",
                        "timestamp": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        404: {
            "description": "对话不存在",
            "content": {
                "application/json": {
                    "example": {"detail": "Conversation conv-1234567890 not found"}
                }
            },
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Browser control error: Failed to input text in any textarea"
                    }
                }
            },
        },
    },
)
async def send_message(
    conversation_id: str,
    message_data: MessageRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """
    发送消息到对话

    向指定的ChatGPT对话发送新消息。系统会自动打开对话页面，
    输入消息内容并点击发送按钮，同时在数据库中记录消息。

    Args:
        conversation_id: 目标对话的ID
        message_data: 消息内容，包含要发送的文本
        request: HTTP请求对象，用于获取WebSocket服务器
        db: 数据库会话

    Returns:
        MessageResponse: 消息发送结果，包含消息ID和发送时间

    Raises:
        HTTPException:
            - 404: 对话不存在
            - 500: 浏览器控制错误或其他内部错误
    """
    try:
        websocket_server = get_websocket_server(request)

        # Verify conversation exists
        query = select(Conversation).where(Conversation.id == conversation_id)
        result = await db.execute(query)
        conversation = result.scalar_one_or_none()

        if not conversation:
            raise NotFoundError(f"Conversation {conversation_id} not found")

        # Open the conversation URL
        conversation_url = f"https://chatgpt.com/c/{conversation_id}"
        logger.info(f"🚀 Opening conversation URL: {conversation_url}")

        # Send command to open the conversation
        command_data = {
            "url": conversation_url,
            "newTab": False,
            "focus": True,
        }

        success = await websocket_server.send_command(
            MessageType.OPEN_CHATGPT, command_data
        )

        if not success:
            raise BrowserControlError("open_chatgpt", "No browser extension connected")

        # Wait for page to load
        logger.info("⏳ Waiting for conversation page to load...")
        page_loaded = await wait_for_page_load(websocket_server, timeout=10.0)
        if not page_loaded:
            logger.warning("⚠️ Page load timeout, continuing anyway...")

        await asyncio.sleep(5.0)

        # Locate and input text in the prompt textarea
        logger.info(f"📝 Inputting message: {message_data.message[:50]}...")

        # Try multiple possible selectors for the textarea
        textarea_selectors = [
            TEXTAREA_XPATH,
        ]

        input_success = False
        for selector in textarea_selectors:
            logger.debug(f"🎯 Trying textarea selector: {selector}")
            input_success = await input_text_by_xpath(
                websocket_server, selector, message_data.message, timeout=10.0
            )
            if input_success:
                logger.info(f"✅ Successfully input text using selector: {selector}")
                break

        if not input_success:
            raise BrowserControlError(
                "input_text", "Failed to input text in any textarea"
            )

        # Wait a moment for the text to be processed
        await asyncio.sleep(1.0)

        # Send message
        logger.info("🔘 Send key to send message...")
        success = await simulate_keyboard_by_xpath(
            websocket_server, TEXTAREA_XPATH, "Enter", timeout=10.0
        )

        if not success:
            raise BrowserControlError("simulate_keyboard", "Failed to simulate Enter key")

        # Create message record in database
        current_time = datetime.now()
        message = Message(
            conversation_id=conversation_id,
            role="user",
            content=message_data.message,
            source="user",  # Mark as user-created message
            sent_at=current_time,
        )

        db.add(message)

        # Update conversation timestamp and set to pending status
        conversation.updated_at = datetime.now()
        conversation.last_accessed = datetime.now()
        conversation.status = ConversationStatus.PENDING  # 设置为等待状态

        await db.commit()

        # Refresh to get the auto-generated ID
        await db.refresh(message)

        # 添加到后台更新池
        try:
            await pending_manager.add_pending_conversation(conversation_id, auto_config=True)
            logger.info(f"➕ Added conversation {conversation_id} to pending update pool")
        except Exception as e:
            logger.warning(f"⚠️ Failed to add conversation to pending pool: {e}")

        logger.success(
            f"✅ Message sent successfully to conversation {conversation_id}"
        )

        return MessageResponse(
            success=True,
            message="Message sent successfully",
            message_id=str(message.id),
            sent_at=message.sent_at or current_time,
        )

    except Exception as e:
        logger.error(f"❌ Error sending message to conversation {conversation_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

